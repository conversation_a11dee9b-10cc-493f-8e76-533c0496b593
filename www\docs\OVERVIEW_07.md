# GPS Pokemon App - Project Overview

## Directory Structure

```
www/
├── capacitor/                    # Capacitor plugin helpers
│   ├── app.js                   # App plugin wrapper with back button handling
│   ├── geolocation.js           # Geolocation plugin with browser fallback
│   ├── keep-awake.js            # Keep screen awake functionality
│   └── time-events.js           # Time-based event management and Pokemon spawn persistence
├── components/                   # Reusable UI components
│   └── EncounterListItem.js     # Individual encounter list item component
├── docs/                        # Documentation files
│   ├── BROWSER_TESTING_GUIDE.md # Guide for browser testing with GPS functionality
│   ├── POKEMON_SPAWNING_FIX.md  # Technical documentation for Pokemon spawning system
│   └── TODOS.md                 # Task list and known issues
├── fonts/                       # Poppins font family files
│   ├── Poppins-*.ttf           # Various Poppins font weights and styles
│   └── poppins.css             # Font face declarations
├── icons/                       # SVG icons
│   └── materialicons/          # Material Design icons for UI elements
│       ├── buddy.svg           # Buddy Pokemon icon
│       ├── bug.svg             # Debug mode icon
│       ├── bye.svg             # Exit/logout icon
│       ├── checkball.svg       # Caught Pokemon icon
│       ├── checklist.svg       # Pokedex icon
│       ├── chevronleft.svg     # Back navigation icon
│       ├── close.svg           # Close/cancel icon
│       ├── eye.svg             # Encounters view icon
│       ├── login.svg           # Login icon
│       ├── logout.svg          # Logout icon
│       ├── menu.svg            # Menu/submenu icon
│       ├── mylocation.svg      # GPS location icon
│       └── swords.svg          # Battle icon
├── lib/                         # External libraries
│   ├── turf.js                 # Turf.js geospatial library (unminified)
│   └── turf.min.js             # Turf.js geospatial library (minified)
├── overlays/                    # (Empty directory for future overlay content)
├── screens/                     # (Empty directory for future screen content)
├── services/                    # Core business logic services
│   ├── battle-calc.js          # Battle calculation engine with type effectiveness
│   ├── battle-session.js       # Trainer battle session management
│   ├── experience-system.js    # Pokemon experience and leveling system
│   ├── map-renderer.js         # Leaflet map rendering and management
│   ├── player-renderer.js      # Player sprite rendering and animation
│   ├── pokemon-manager.js      # Central Pokemon data management service
│   ├── pokemon-spawner.js      # Pokemon spawning logic with grid system
│   ├── spawn-levels.js         # Pokemon level calculation for spawns
│   └── trainer-spawner.js      # NPC trainer spawning system
├── src/                         # Static assets
│   ├── NPCNames/               # NPC name data
│   │   └── NPCNames.json       # Random trainer names database
│   ├── NPCSprites/             # Trainer sprite assets
│   │   └── trainers/           # Individual trainer sprites (Battle/Map variants)
│   ├── PlayerSprites/          # Player character sprites
│   │   ├── female/             # Female player sprites (8 directions × 3 frames)
│   │   ├── male/               # Male player sprites (8 directions × 3 frames)
│   │   └── playerTrainer.png   # Static trainer battle sprite
│   ├── PokemonSprites/         # Pokemon sprite images
│   │   └── [0-151].png         # Individual Pokemon sprites by Pokedex number
│   └── battleBackgrounds/      # Battle scene backgrounds
│       └── bb_forest.png       # Forest battle background
├── state/                       # Application state management
│   └── game-state.js           # Central game state container
├── storage/                     # Data persistence layer
│   ├── caughtPokemonStorage.js # Caught Pokemon storage wrapper
│   ├── encountersStorage.js    # Pokemon encounter history storage
│   ├── storage-service.js      # Unified storage service (Capacitor/localStorage)
│   └── teamStorage.js          # Player team management storage
├── styles/                      # CSS stylesheets
│   ├── battle-screen.css       # Battle screen specific styles
│   ├── common-screens.css      # Shared screen styles
│   ├── encounters-screen.css   # Encounters screen styles
│   ├── fab-submenu.css         # FAB submenu animation styles
│   ├── player.css              # Player sprite animation styles
│   ├── pokedex.css             # Pokedex screen styles
│   ├── pokemon-caught-screen.css # Pokemon management screen styles
│   ├── style.css               # Main application styles
│   ├── trainer-battle.css      # Trainer battle screen styles
│   ├── type-colors.css         # Pokemon type color definitions
│   └── variables-gui.css       # CSS custom properties and color variables
├── tests/                       # Test files and utilities
│   ├── evolution-test.html     # Pokemon evolution testing interface
│   ├── evolution-test.js       # Pokemon evolution test logic
│   ├── index.html              # Test suite index page
│   ├── pokemon-storage-test.html # Pokemon storage testing interface
│   ├── pokemon-storage-test.js # Pokemon storage test logic
│   ├── run-xp-test.js          # Experience system consistency tests
│   ├── spawn-levels-test.js    # Spawn level calculation tests
│   ├── trainer-battle-test.html # Trainer battle testing interface
│   ├── xp-consistency-test.js  # XP system validation tests
│   ├── xp-test.html            # XP system testing interface
│   └── [data files]            # Test data copies (pokedex, types, etc.)
├── ui/                          # User interface components
│   ├── BattleScreen.js         # Wild Pokemon battle interface
│   ├── Component.js            # Base UI component class
│   ├── EncountersScreen.js     # Pokemon encounters history screen
│   ├── FabManager.js           # Floating Action Button management
│   ├── FabSubmenuManager.js    # FAB submenu system
│   ├── PokedexScreen.js        # Pokedex viewing interface
│   ├── PokemonCaughtScreen.js  # Caught Pokemon and team management
│   └── TrainerBattleScreen.js  # NPC trainer battle interface
├── utils/                       # Utility functions
│   ├── battle-utils.js         # Shared battle screen utilities
│   ├── logger.js               # Centralized logging service
│   ├── pokemon-display-names.js # Pokemon name localization utilities
│   └── pokemon-utils.js        # Pokemon data manipulation utilities
├── config.js                    # Application configuration constants
├── debug.svg                    # Debug mode indicator icon
├── index.html                   # Main HTML entry point
├── landuse-pokemon-types.js     # Landuse-based Pokemon type mapping
├── main.js                      # Application entry point and initialization
├── overpass-landuse.js          # OpenStreetMap landuse data fetching
├── pokedex-151.json            # Complete Pokemon database (Gen 1)
├── pokemon-grid.js             # Grid-based Pokemon spawning system
├── pokemon-types-battle.json   # Type effectiveness chart for battles
├── Pokemon.js                  # Pokemon class definition
├── starter-pokemon.json        # Starter Pokemon configuration
├── Trainer.js                  # Trainer class definition
└── trainerTypes.json           # Trainer type definitions and sprites
```

## Key Files and Their Functions

### Core Application Files

**main.js** - Application entry point that initializes all systems, sets up error handlers, manages GPS location watching, and coordinates between map rendering, Pokemon spawning, and UI management.

**config.js** - Centralized configuration containing map settings, Pokemon spawn parameters, UI dimensions, storage keys, and geolocation options.

**index.html** - Main HTML document that loads all CSS stylesheets, includes Leaflet map library, and provides the basic DOM structure for the application.

### Data Models

**Pokemon.js** - Pokemon class with experience system integration, evolution handling, JSON serialization, and comprehensive property management for caught and wild Pokemon.

**Trainer.js** - Trainer class for NPC trainers with team management, sprite path resolution, position tracking, and battle statistics.

### Core Services

**services/pokemon-manager.js** - Central Pokemon data management service handling storage, team management, caught Pokemon tracking, and data persistence across app sessions.

**services/pokemon-spawner.js** - Advanced Pokemon spawning system using grid-based location tracking, landuse-aware special spawns, and time-based spawn persistence.

**services/battle-calc.js** - Battle calculation engine implementing custom type effectiveness multipliers, damage calculations, and experience point awards.

**services/experience-system.js** - Pokemon leveling system with rarity-based growth curves, level calculations, and experience point management.

**services/map-renderer.js** - Leaflet map integration with Pokemon marker management, trainer marker rendering, and interactive popup systems.

### User Interface

**ui/BattleScreen.js** - Wild Pokemon battle interface with animated Pokemon sprites, type effectiveness display, experience bars, and battle result processing.

**ui/TrainerBattleScreen.js** - NPC trainer battle system with round-by-round combat, Pokemon selection interface, and comprehensive battle statistics.

**ui/PokedexScreen.js** - Pokemon catalog interface showing caught/seen status, detailed Pokemon information, and completion progress tracking.

**ui/PokemonCaughtScreen.js** - Team management interface allowing Pokemon viewing, team composition changes, buddy selection, and Pokemon release functionality.

**ui/FabManager.js** - Floating Action Button system managing main navigation buttons with grid-based positioning and debug mode integration.

### Storage and State

**storage/storage-service.js** - Unified storage abstraction supporting both Capacitor native storage and browser localStorage with automatic fallback.

**state/game-state.js** - Central application state container managing map references, Pokemon collections, trainer data, and UI state persistence.

**capacitor/time-events.js** - Time-based event system managing hourly Pokemon spawn cycles, storage synchronization, and spawn data persistence.

### Utilities and Helpers

**utils/logger.js** - Comprehensive logging system with Capacitor/browser environment detection, multiple log levels, and caller information tracking.

**utils/battle-utils.js** - Shared battle screen utilities including experience bar rendering, type badge updates, Pokemon indicators, and battle animations.

**utils/pokemon-display-names.js** - Pokemon name localization system providing German display names while maintaining English internal references.

## Data Files and Configuration

**pokedex-151.json** - Complete Pokemon database containing all Generation 1 Pokemon with names, types, evolution data, German translations, rarity classifications, and sprite URLs.

**pokemon-types-battle.json** - Type effectiveness chart defining battle multipliers (1.25×, 1.0×, 0.8×, 0.2×) for all Pokemon type combinations.

**trainerTypes.json** - Trainer class definitions including sprite paths, gender variants, level ranges, and Pokemon team compositions for NPC trainers.

**starter-pokemon.json** - Configuration for starter Pokemon selection including Pikachu as the default buddy Pokemon at level 5.

**landuse-pokemon-types.js** - Mapping system connecting OpenStreetMap landuse data to specific Pokemon types for location-based special spawns.

## Capacitor Integration

**capacitor/geolocation.js** - GPS location services with automatic fallback from Capacitor native geolocation to browser Geolocation API, including test mode support.

**capacitor/keep-awake.js** - Screen wake lock management preventing device sleep during active gameplay sessions.

**capacitor/app.js** - Hardware back button handling with double-tap exit functionality and overlay-specific navigation management.

## Testing Infrastructure

**tests/** - Comprehensive test suite including Pokemon evolution tests, storage consistency validation, experience system verification, and trainer battle simulation.

## Asset Organization

**src/PokemonSprites/** - Complete sprite collection for all 151 Generation 1 Pokemon with consistent naming convention (0.png through 151.png).

**src/NPCSprites/trainers/** - Extensive trainer sprite library with separate Battle and Map variants for diverse trainer types and genders.

**src/PlayerSprites/** - Animated player character sprites supporting 8-directional movement with 3-frame walking animations for both male and female characters.

## Architecture Highlights

### Modular Design
The application follows a clean separation of concerns with distinct layers for data models, business logic services, user interface components, and storage management.

### Cross-Platform Compatibility
Built with Capacitor for native mobile deployment while maintaining full browser compatibility for development and testing.

### Real-Time GPS Integration
Advanced location-based gameplay using Turf.js for geospatial calculations, grid-based spawning systems, and landuse-aware Pokemon distribution.

### Comprehensive Battle System
Feature-complete battle mechanics with type effectiveness, experience systems, trainer battles, and animated battle interfaces.

### Persistent Game State
Robust data persistence using unified storage services supporting both native and web environments with automatic synchronization.

### Extensible Component System
Base component architecture enabling consistent UI development with proper event handling and lifecycle management.

This architecture provides a solid foundation for a location-based Pokemon game with room for future expansion and feature additions.
